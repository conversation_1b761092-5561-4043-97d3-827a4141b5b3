import {
  ActivityLog,
  ActivityLogPayload,
  cancelEscrowPayment,
  formatDate,
  getDueDate,
  Order,
  releaseEscrowPayment,
} from "@/services/ordersServices";
import { FieldValue, getFirestore, Timestamp } from "firebase-admin/firestore";
import {
  getOrderInfoDesc,
  getOrderStatusDesc,
  OrderActivityType,
  OrderInfo,
  OrderInfoName,
  OrderStatusType,
} from "../constant";
import { getIdToken } from "../utils";
// import { Timestamp } from "firebase/firestore";
// import { GetUserInfo } from "@/services/usersServices";
import { MailServiceManager } from "@/services/MailService";
import { SendMailHandler } from "./handlers";
import { NotificationHandlerManager } from "./notification-handlers";
import { NotificationEvents } from "@/services/notificationService";
import { Stripe } from "stripe";
import { User } from "@/services/UserInterface";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

export class OrdersHandlerManager {
  private USERS_COLLECTION = "users";
  private ORDERS_COLLECTION = "orders";

  static instance: OrdersHandlerManager | null = null;

  private constructor() {}

  static getInstance() {
    if (!this.instance) {
      this.instance = new OrdersHandlerManager();
    }
    return this.instance;
  }

   private _sanitizeUserPayload(data: User) {
      const { basketOrdersCount, password, ...safeData } = data;
  
      return safeData;
    }
  getOrderById = async (
    id: string
  ): Promise<{ success: true; order: Order } | { success: false; error: string }> => {
    try {
      const db = getFirestore();
      const docRef = db.collection(this.ORDERS_COLLECTION).doc(id);
      const docSnap = await docRef.get();

      console.log({ docSnap: docSnap?.exists });

      if (!docSnap.exists) {
        return { success: false, error: "Order not found" };
      }
      console.log({ data: docSnap.data() });

      return {
        success: true,
        order: { id: docSnap.id, ...docSnap.data() } as Order,
      };
    } catch (error) {
      console.error("Error fetching order by ID:", error);
      return { success: false, error: "Failed to fetch order" };
    }
  };

  markEscrowStageReleased = async (
    orderId: string,
    stage: "accept" | "delivered" | "completed",
    amount: number,
    transferId: string
  ): Promise<{ success: boolean; idempotent?: boolean; error?: string }> => {
    try {
      const db = getFirestore();
      const orderRef = db.collection("orders").doc(orderId);

      return await db.runTransaction(async (tx) => {
        const snap = await tx.get(orderRef);
        if (!snap.exists) {
          return { success: false, error: "Order not found" };
        }

        const data = snap.data() as { releasedStages?: any[] };
        const releasedStages = (data?.releasedStages || []) as Array<{
          stage: string;
          transferId: string;
        }>;

        const already = releasedStages.find((s) => s.stage === stage);
        if (already) {
          return { success: true, idempotent: true };
        }

        const newEntry = {
          stage,
          amount,
          transferId,
          releasedAt: FieldValue.serverTimestamp(),
        };

        tx.update(orderRef, {
          releasedStages: [...releasedStages, newEntry],
        });

        return { success: true };
      });
    } catch (e) {
      console.error("markEscrowStageReleased failed:", e);
      return { success: false, error: "Failed to mark stage released" };
    }
  };

  updateOrder = async (
    id: string,
    updatedData: Partial<Omit<Order, "id" | "added_at">>
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      const db = getFirestore();
      const orderRef = db.collection("orders").doc(id);

      await orderRef.update({
        ...updatedData,
        added_at: FieldValue.serverTimestamp(),
      });

      return { success: true };
    } catch (error) {
      console.error("Error updating order:", error);
      return { success: false, error: "Failed to update order" };
    }
  };

  GetOrderDetailsByUserId = async ({ userId, status }: { userId: string; status?: string }) => {
    try {
      const db = getFirestore();
      const ordersRef = db.collection("orders");
      const usersRef = db.collection("users");
      const servicesRef = db.collection("services");

      // Query 1: My Orders (where profileId === userId)
      let myOrdersQuery = ordersRef
        .where("userProfileId", "==", userId)
        .orderBy("added_at", "desc");
      if (status) {
        myOrdersQuery = myOrdersQuery.where("status", "==", status);
      }

      // Query 2: Received Orders (where profileId === userId)
      let receivedOrdersQuery = ordersRef
        .where("profileId", "==", userId)
        .orderBy("added_at", "desc");
      if (status) {
        receivedOrdersQuery = receivedOrdersQuery.where("status", "==", status);
      }

      const [myOrdersSnapshot, receivedOrdersSnapshot] = await Promise.all([
        myOrdersQuery.get(),
        receivedOrdersQuery.get(),
      ]);

      // Combine all orders
      const allOrderDocs = [...myOrdersSnapshot.docs, ...receivedOrdersSnapshot.docs];

      // Collect unique user IDs & service IDs
      const userIds = new Set<string>();
      const serviceIds = new Set<string>();

      allOrderDocs.forEach((doc) => {
        const data = doc.data();
        userIds.add(data.userProfileId);
        userIds.add(data.profileId);
        serviceIds.add(data.serviceId);
      });

      // Fetch all involved users
      const userDocs = await Promise.all(
        Array.from(userIds).map(async (id) => {
          const snap = await usersRef.doc(id).get();
          return snap.exists ? { id, ...snap.data() } : null;
        })
      );
      const userMap = Object.fromEntries(userDocs.filter(Boolean).map((u) => [u!.id, u]));

      // Fetch all services
      const serviceDocs = await Promise.all(
        Array.from(serviceIds).map(async (id) => {
          const serviceSnap = await servicesRef.doc(id).get();
          if (!serviceSnap.exists) return null;

          const customizationsSnap = await db
            .collection("services")
            .doc(id)
            .collection("customizations")
            .get();

          const customizations = customizationsSnap.docs
            .map((c) => {
              const data = c.data();
              return {
                id: c.id,
                ...data,
                price: parseFloat((data.price / (1 - 0.16)).toFixed(2))?.toString(),
              };
            })
            .filter((curr) => serviceSnap.data()?.customizations?.includes(curr?.id));

          const serviceDetail = serviceSnap.data() || {};
          serviceDetail.price = parseFloat(
            (serviceDetail.price / (1 - 0.16)).toFixed(2)
          )?.toString();

          return {
            id,
            ...serviceDetail,
            customizations,
          };
        })
      );
      const serviceMap = Object.fromEntries(serviceDocs.filter(Boolean).map((s) => [s!.id, s]));

      // Helper: fetch activity log
      const fetchActivityLog = async (orderId: string) => {
        const activityLogSnap = await db
          .collection("orders")
          .doc(orderId)
          .collection("activityLog")
          .get();

        return activityLogSnap.docs
          .map((d) => ({ id: d.id, ...(d.data() as ActivityLog) }))
          ?.map((c) => {
            return {
              ...c,
              // date:c.date instanceof Timestamp
              // ? c.date.toDate().toISOString() // or .toMillis()
              // : c.date,
            };
          })
          .sort((a, b) => {
            const dateA =
              a.date && typeof a.date.toDate === "function" ? a.date.toDate().getTime() : 0;
            const dateB =
              b.date && typeof b.date.toDate === "function" ? b.date.toDate().getTime() : 0;

            if (dateB !== dateA) return dateB - dateA;

            const priority = (type: string) => {
              if (type === "Order info") return 1;
              if (type === "Order status update") return 2;
              return 3;
            };
            return priority(a.type) - priority(b.type);
          });
      };

      // Enrich orders
      const enrichOrder = async (docSnap: FirebaseFirestore.QueryDocumentSnapshot) => {
        const data = docSnap.data() as Order;
        const orderId = docSnap.id;

        return {
          id: orderId,
          ...data,
          activityLog: await fetchActivityLog(orderId),
          userProfileDetails: userMap[data.userProfileId] || null,
          profileDetails: userMap[data.profileId] || null,
          serviceDetails: serviceMap[data.serviceId] || null,
        };
      };

      const allOrders = await Promise.all(allOrderDocs.map(enrichOrder));

      // Split into my_orders and received_orders
      let my_orders = allOrders
        .filter((o) => o.userProfileId === userId)
        ?.map((c) => {
          return {
            ...c,
            dueDate:
              c.dueDate instanceof Timestamp
                ? c.dueDate.toDate().toISOString() // or .toMillis()
                : c.dueDate,
            added_at:
              c.added_at instanceof Timestamp
                ? c.added_at.toDate().toISOString() // or .toMillis()
                : c.added_at,
          };
        });
      let received_orders = allOrders
        .filter((o) => o.profileId === userId)
        ?.map((c) => {
          return {
            ...c,
            dueDate:
              c.dueDate instanceof Timestamp
                ? c.dueDate.toDate().toISOString() // or .toMillis()
                : c.dueDate,
            added_at:
              c.added_at instanceof Timestamp
                ? c.added_at.toDate().toISOString() // or .toMillis()
                : c.added_at,
          };
        });

      let basket: any[] = [];

      if (status === "BASKET") {
        my_orders = my_orders.filter((current) => current.status === "BASKET");
      } else {
        const temp_my_orders = my_orders;
        my_orders = my_orders.filter((current) => current.status !== "BASKET");
        received_orders = received_orders.filter((current) => current.status !== "BASKET");
        basket = temp_my_orders.filter((current) => current.status === "BASKET");
      }

      // Sort by added_at
      const sorter = (a: any, b: any) =>
        (b.added_at?.toMillis?.() || 0) - (a.added_at?.toMillis?.() || 0);

      my_orders = my_orders.sort(sorter);
      received_orders = received_orders.sort(sorter);
      basket = basket.sort(sorter);

      // hide more things in this
      my_orders = my_orders?.map((c)=>{
        return { 
          ...c,
          userProfileDetails:this._sanitizeUserPayload(c?.userProfileDetails as User),
          profileDetails:this._sanitizeUserPayload(c?.profileDetails as User),
        }
      })
      received_orders = received_orders?.map((c)=>{
        return { 
          ...c,
          userProfileDetails:this._sanitizeUserPayload(c?.userProfileDetails as User),
          profileDetails:this._sanitizeUserPayload(c?.profileDetails as User),
        }
      })
      //
      // sanatize this 
      basket = basket?.map((c)=>{
        return { 
          ...c,
          userProfileDetails:this._sanitizeUserPayload(c?.userProfileDetails as User),
          profileDetails:this._sanitizeUserPayload(c?.profileDetails as User),
        }
      })

      

      return { my_orders, received_orders, basket };
    } catch (error) {
      console.error("GetOrderDetailsByUserId failed:", error);
      throw new Error("getOrderDetailsByUserIdFailed");
    }
  };

  CreateOrder_V2 = async ({ orderData }: { orderData: Order }) => {
    try {
      const db = getFirestore();

      // Validate required fields
      if (!orderData.userProfileId) throw new Error("userProfileId is required");
      if (!orderData.profileId) throw new Error("profileId is required");
      if (!orderData.serviceId) throw new Error("serviceId is required");
      if (!orderData.status) throw new Error("status is required");

      // Check for existing order in basket
      const existingSnapshot = await db
        .collection("orders")
        .where("userProfileId", "==", orderData.userProfileId)
        .where("profileId", "==", orderData.profileId)
        .where("serviceId", "==", orderData.serviceId)
        .where("status", "==", "BASKET")
        .get();

      if (!existingSnapshot.empty) {
        return {
          success: false,
          error: "Service already exists in basket",
        };
      }

      // Try to find user with serviceId inside `serviceIds` array
      const usersSnap = await db
        .collection("users")
        .where("serviceIds", "array-contains", orderData.serviceId)
        .get();

      let userData: FirebaseFirestore.DocumentData | null = null;

      if (usersSnap.empty) {
        // fallback: get user by profileId
        const userDoc = await db.collection("users").doc(orderData.profileId).get();
        if (!userDoc.exists) {
          throw new Error(`No user found with profileId: ${orderData.profileId}`);
        }
        userData = userDoc.data() || {};
      } else {
        userData = usersSnap.docs[0].data();
      }

      // Create the order
      const newOrder = {
        ...orderData,
        added_at: Timestamp.now(), // Admin SDK replacement for serverTimestamp()
        isUS: userData?.currency?.toLowerCase() === "usd",
        currency: userData?.currency || "gbp",
      };

      const docRef = await db.collection("orders").add(newOrder);

      return { success: true, id: docRef.id };
    } catch (error) {
      console.error("CreateOrder_V2 error details:", error);
      throw new Error(
        `CreateOrder_V2_Failed: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  };

  generateNextOrderId(n: number): string {
    return `A${String(n + 1).padStart(6, "0")}`;
  }

  AddtoOrderState = async ({
    id,
    dueDate,
    loggedInUser,
    sellerName,
    userName,
    sendInvoice,
    payment_intent_id,
  }: {
    id: string;
    dueDate?: FirebaseFirestore.Timestamp;
    loggedInUser: string;
    sellerName: string;
    userName: string;
    sendInvoice?: boolean;
    payment_intent_id?: string;
  }) => {
    try {
      const db = getFirestore();

      const orderRef = db.collection("orders").doc(id);
      const orderSnap = await orderRef.get();

      if (!orderSnap.exists) {
        throw new Error("Order not found");
      }

      const order = orderSnap.data() as Order;

      if (order.status !== "BASKET") {
        throw new Error("Order is not in basket state");
      }

      const serviceRef = db.collection("services").doc(order.serviceId);
      const serviceSnap = await serviceRef.get();

      if (!serviceSnap.exists) {
        throw new Error("Service not found");
      }

      const serviceModel: any = serviceSnap.data();

      // --- Service Price Calculation ---
      serviceModel.servicePrice = serviceModel?.price;
      serviceModel.price = parseFloat((serviceModel?.price / (1 - 0.16)).toFixed(2)).toString();

      // --- Customizations ---
      let customizationsData: any[] = [];
      if (order.selectedCustomizations?.length) {
        const customizationRefs = order.selectedCustomizations.map((cid) =>
          db.collection("services").doc(order.serviceId).collection("customizations").doc(cid)
        );

        const customizationSnaps = await Promise.all(customizationRefs.map((ref) => ref.get()));

        customizationsData = customizationSnaps
          .filter((snap) => snap.exists)
          .map((snap) => ({ id: snap.id, ...snap.data() }));
      }

      serviceModel.customizationsModels = customizationsData.map((c) => ({
        ...c,
        originalPrice: c.price,
        price: parseFloat((c.price / (1 - 0.16)).toFixed(2)).toString(),
      }));

      // --- Generate Unique Order Id ---
      const orderIdsRef = db.collection("ordersIds");
      const snapshot = await orderIdsRef.get();
      const count = snapshot.size;

      const newUniqueId = this.generateNextOrderId(count);

      await db
        .collection("ordersIds")
        .doc(newUniqueId)
        .set({
          orderIds: [id],
        });

      // --- Update Order ---
      let updatePayload: any = {
        status: OrderStatusType.NEW,
        uniqueId: newUniqueId,
        serviceModel,
        dueDate: Timestamp.fromDate(new Date(Date.now() + 48 * 60 * 60 * 1000)),
        added_at: Timestamp.now(),
      };

      if (dueDate) {
        updatePayload.specificDueDate = dueDate;
      }

      if (sendInvoice) {
        await this.sendInvoiceRequest(id);
      }

      await orderRef.update(updatePayload);

      if (payment_intent_id) {
        const paymentIntent = await stripe.paymentIntents.retrieve(payment_intent_id);

        console.log("✅ Payment intent retrieved:", {
          id: paymentIntent.id,
          status: paymentIntent.status,
          amount: paymentIntent.amount,
          currency: paymentIntent.currency,
          charge: paymentIntent.latest_charge,
        });
        this.UpdateOrderStripeDetails({
          id,
          chargeId: paymentIntent.latest_charge?.toString() ?? "",
          transactionId: payment_intent_id,
        });

        // update description
        const updated = await stripe.paymentIntents.update(payment_intent_id, {
          description: `Payment to Nascent Ventures - Order ${newUniqueId}`,
          metadata: {
            order_id: newUniqueId,
          },
        });
        console.log({ updated });
      }

      // --- Add Activity Log ---
      await this.UpdateActivityLog({
        orderId: orderSnap.id,
        description: "",
        from: "user",
        title: OrderStatusType.NEW,
        loggedInUser,
        sellerName,
        userName,
        reason: "asdd",
      });

      return { success: true, newUniqueId };
    } catch (error) {
      console.error("AddtoOrderState_Failed", error);
      throw new Error("AddtoOrderState_Failed");
    }
  };
  sendInvoiceRequest = async (orderId: string) => {
    try {
      // Fetch order by ID
      const orderResult = await this.getOrderById(orderId);
      if (!orderResult?.success) {
        return;
      }
      const uniqueId: string = orderResult?.order?.uniqueId ?? "";

      // Lookup user email using Admin SDK
      const userRecord = await this.GetUserInfo(orderResult?.order?.userProfileId);
      const customer_email: string = userRecord?.email ?? "";

      // Or, if you want from Firestore instead of Auth:
      // const customer_email: string = (await GetUserInfo(orderResult?.order?.userId))?.email;

      // Send mail via your Mail Service
      await SendMailHandler({
        payload: {
          to: customer_email,
        },
        type: "invoice_request",
        mail_type: "invoice_request",
        message: {
          orderId: uniqueId,
        },
      });

      console.log(`✅ Invoice request sent for order ${uniqueId} to ${customer_email}`);
    } catch (error) {
      console.error("❌ Failed to send invoice request:", error);
      throw error;
    }
  };

  UpdateUserAccountId = async ({ user_id, stripe_id }: { user_id: string; stripe_id: string }) => {
    try {
      const db = getFirestore();

      const usersRef = db.collection("users").doc(user_id);

      await usersRef.update({
        stripe_id,
      });

      return { success: true };
    } catch (error) {
      console.error("UpdateUserAccountId_Failed", error);
      return { success: false, message: "UpdateUserAccountId_Failed" };
    }
  };
  UpdateOrderStripeDetails = async ({
    id, // order document id
    chargeId,
    transactionId,
  }: {
    id: string;
    chargeId: string;
    transactionId: string;
  }) => {
    try {
      const db = getFirestore();

      const orderRef = db.collection("orders").doc(id);

      await orderRef.update({
        chargeId,
        transactionId,
      });

      console.log(`Order ${id} updated with Stripe details`);

      return { success: true };
    } catch (error) {
      console.error("UpdateOrderStripeDetails_Failed:", error);
      return {
        success: false,
        message: "UpdateOrderStripeDetails_Failed",
      };
    }
  };

  UpdateActivityLog = async ({
    orderId,
    description,
    from,
    newDueDate, // optional
    title,
    loggedInUser,
    sellerName,
    userName,
    reason,
  }: Partial<ActivityLogPayload>) => {
    try {
      const db = getFirestore();

      if (!loggedInUser) return { success: false, message: "loggedInUser not found" };
      if (!sellerName) return { success: false, message: "sellerName not found" };
      if (!userName) return { success: false, message: "userName not found" };
      if (!orderId) return { success: false, message: "orderId not found" };
      if (!reason) return { success: false, message: "reason not found" };
      if (!title) return { success: false, message: "title not found" };

      // fetch unique orderId
      const docSnap = await db.collection("orders").doc(orderId).get();
      if (!docSnap.exists) {
        return { success: false, error: "order not found" };
      }
      const uniqueOrderId = docSnap.data()?.uniqueId;

      // payloads
      let activityLogStatusPayload: Partial<ActivityLog> = {
        orderId: uniqueOrderId,
        title,
        date: Timestamp.now(),
        type: OrderActivityType.orderStatusUpdate,
      };

      let activityLogInfoPayload: Partial<ActivityLog> = {
        orderId: uniqueOrderId,
        date: Timestamp.now(),
        type: OrderActivityType.orderInfo,
      };

      // decide OrderInfo
      let orderInfoStatus: OrderInfo | undefined;
      switch (title) {
        case OrderStatusType.NEW:
          orderInfoStatus = OrderInfo.full;
          break;
        case OrderStatusType.REFUND_REQUEST:
          break;
        case OrderStatusType.REFUNDED:
          orderInfoStatus = OrderInfo.refund;
          break;
        case OrderStatusType.CANCELLATION_REQUEST:
          break;
        case OrderStatusType.CANCELLED:
          break;
        case OrderStatusType.COMPLETED:
          orderInfoStatus = OrderInfo.eighty;
          break;
        case OrderStatusType.DELIVERED:
          orderInfoStatus = OrderInfo.ten;
          break;
        case OrderStatusType.AUTO_COMPLETED:
          orderInfoStatus = OrderInfo.eighty;
          break;
        case OrderStatusType.INCOMPLETE:
          break;
        case OrderStatusType.DECLINED:
          break;
        case OrderStatusType.ACCEPTED:
          orderInfoStatus = OrderInfo.ten;
          break;
        case OrderStatusType.REVISION_REQUEST:
          break;
        case OrderStatusType.AUTO_DECLINED:
          break;
        case OrderStatusType.BASKET:
          break;
        default:
          break;
      }

      const _activityLogRef = db.collection("orders").doc(orderId).collection("activityLog");

      // check for revision requests
      const revisionSnapshot = await _activityLogRef
        .where("title", "==", OrderStatusType.REVISION_REQUEST)
        .get();

      if (!revisionSnapshot.empty && orderInfoStatus === OrderInfo.ten) {
        orderInfoStatus = undefined;
      }

      // order info handling
      if (orderInfoStatus) {
        let orderInfoPayload: any = {
          loggedInUser,
          sellerName,
          status: orderInfoStatus,
        };
        if (newDueDate) {
          orderInfoPayload.newDateModel = {
            formattedDate: newDueDate,
            comment: description ?? "",
            reason,
            orderId,
          };
        }
        const orderInfoDesc = getOrderInfoDesc(orderInfoPayload);
        activityLogInfoPayload = {
          ...activityLogInfoPayload,
          title: OrderInfoName[orderInfoStatus],
          description: orderInfoDesc,
        } as ActivityLog;

        if (orderInfoStatus === OrderInfo.ten) {
          if (title === OrderStatusType.DELIVERED) {
            const resp = await releaseEscrowPayment({
              orderId,
              stage: "delivered",
              from,
              sellerName,
              title,
              uniqueOrderId,
              userName,
              description,
              newDueDate,
              orderInfoStatus,
              reason,
              loggedInUser
            });
            if (!resp.success) {
              console.log(`${title} realsase paymant failed`);

              throw new Error("Something went wrong ");
            }
            // handled by webhook
             return "ok";
          } else if (title === OrderStatusType.ACCEPTED) {
            const resp = await releaseEscrowPayment({
              orderId,
              stage: "accept",
              sellerName,
              title,
              uniqueOrderId,
              userName,
              description,
              from,
              newDueDate,
              orderInfoStatus,
              reason,
              loggedInUser
            });
            if (!resp.success) {
              console.log(`${title} realsase paymant failed`);

              throw new Error("Something went wrong ");
            }
            // handled by webhook
              return "ok";
          }
        } else if (orderInfoStatus === OrderInfo.eighty) {
          const resp = await releaseEscrowPayment({
            orderId,
            stage: "completed",
            sellerName,
            title,
            uniqueOrderId,
            userName,
            description,
            from,
            newDueDate,
            orderInfoStatus,
            reason,
            loggedInUser
          });
          if (!resp.success) {
            console.log(`${title} realsase paymant failed`);

            throw new Error("Something went wrong ");
          }
          // handled by webhook
             return "ok";
        } else if (orderInfoStatus === OrderInfo.refund) {
          // refund case
        } else if (orderInfoStatus === OrderInfo.full) {
          // pre-auth case
        }
      }

      if (title === OrderStatusType.DECLINED) {
        const resp = await cancelEscrowPayment({
          orderId,
            from,
              sellerName,
              title,
              uniqueOrderId,
              userName,
              description,
              newDueDate,
              orderInfoStatus,
              reason,
              loggedInUser       
        });
        if (!resp.success) {
          console.log(`${title} cancel paymant failed`);
          throw new Error("Something went wrong ");
        }
        // 🚀 handle this also by webhok
          return "ok";
      }

      //////////////////////////////////////////////////////////////////////////////

      // order status desc
      const orderStatusDesc = getOrderStatusDesc({
        profileType: from as "creator" | "user",
        sellerName,
        status: title,
        userName,
        comment: description,
        reason,
      });

      // get last log for previousStatus
      const snapshot = await _activityLogRef
        .where("type", "==", OrderActivityType.orderStatusUpdate)
        .orderBy("date", "desc")
        .limit(1)
        .get();

      let previousStatus: OrderStatusType | undefined;
      if (!snapshot.empty) {
        previousStatus = snapshot.docs[0].data()?.title;
      }

      activityLogStatusPayload = {
        ...activityLogStatusPayload,
        title,
        from,
        description: orderStatusDesc,
      } as ActivityLog;

      if (previousStatus !== undefined) {
        activityLogStatusPayload.previousStatus = previousStatus;
      }
      if (newDueDate !== undefined) {
        activityLogStatusPayload.newDueDate = newDueDate;
        activityLogInfoPayload.newDueDate = newDueDate;
      }

      // update order status
      await db.collection("orders").doc(orderId).update({ status: title });

      // add logs
      if (orderInfoStatus) {
        await Promise.all([
          _activityLogRef.add(activityLogStatusPayload),
          _activityLogRef.add(activityLogInfoPayload),
        ]);
      } else {
        await _activityLogRef.add(activityLogStatusPayload);
      }

      // accepted/delivered => update due date
      if (title === OrderStatusType.ACCEPTED || title === OrderStatusType.DELIVERED) {
        await this.UpdateOrderDueDate(orderId);
      }

      //------------------------------------ send email
      const orderResult = await this.getOrderById(orderId);
      if (!orderResult?.success) {
        return;
      }
      let uniqueId: string = orderResult?.order?.uniqueId ?? "";
      const seller_mail: string = (await this.GetUserInfo(orderResult?.order?.profileId ?? ""))
        ?.email;
      const buyer_mail: string = (await this.GetUserInfo(orderResult?.order?.userProfileId ?? ""))
        ?.email;

      const targetMail = activityLogStatusPayload?.from === "user" ? seller_mail : buyer_mail;

      console.log({ targetMail });

      const targetSrcUserId =
        activityLogStatusPayload?.from === "creator"
          ? orderResult?.order?.profileId
          : orderResult?.order?.userProfileId;

      const targetDestUserId =
        activityLogStatusPayload?.from === "creator"
          ? orderResult?.order?.userProfileId
          : orderResult?.order?.profileId;

      if (title === OrderStatusType.ACCEPTED) {
        await SendMailHandler({
          type: "sendMail",
          mail_type: "order_update",
          message: {
            orderId: uniqueId,
            status: title,
            message: `
            ${activityLogStatusPayload?.description ?? ""}
            Order due date: ${
              orderResult?.order?.dueDate ? formatDate(orderResult?.order?.dueDate) : ""
            }
          `,
          },
          payload: {
            to: targetMail,
          },
        });
      } else {
        await SendMailHandler({
          type: "sendMail",
          mail_type: "order_update",
          message: {
            orderId: uniqueId,
            status: title,
            message: `${activityLogStatusPayload?.description ?? ""}`,
          },
          payload: {
            to: targetMail,
          },
        });
      }

      // notify user

      NotificationHandlerManager.getInstance().CreateNotification({
        payload: {
          src_id: targetSrcUserId,
          dest_id: targetDestUserId,
          event: NotificationEvents.ORDER_STATUS_CHANGE,
          order_id: uniqueId,
          order_status: title,
        },
        check_duplicate: false,
      });

      return "ok";
    } catch (error) {
      console.error("UpdateActivityLog_Failed:", error);
      throw new Error("UpdateActivityLog_Failed");
    }
  };
  UpdateOrderDueDate = async (orderId: string) => {
    try {
      const db = getFirestore();

      const orderRef = db.collection("orders").doc(orderId);
      const orderSnap = await orderRef.get();

      if (!orderSnap.exists) {
        return { success: false, error: "order not found" };
      }

      const order = orderSnap.data() as Order;

      if (order.status === OrderStatusType.ACCEPTED) {
        await orderRef.update({
          dueDate: order.newDueDate || order.specificDueDate || getDueDate([order]),
        });
      }

      if (order.status === OrderStatusType.DELIVERED) {
        await orderRef.update({
          lastDeliveredDate: Timestamp.now(),
        });
      }

      return { success: true };
    } catch (error) {
      console.error("UpdateOrderDueDate failed:", error);
      throw new Error("UpdateOrderDueDate_Failed");
    }
  };
  GetUserInfo = async (uid: string) => {
    try {
      const db = getFirestore();

      const userRef = db.collection("users").doc(uid);
      const userSnap = await userRef.get();

      if (!userSnap.exists) {
        return null;
      }

      return userSnap.data() ?? {};
    } catch (error) {
      console.error("GetUserInfo failed:", error);
      throw new Error("GetUserInfo failed");
    }
  };
}
