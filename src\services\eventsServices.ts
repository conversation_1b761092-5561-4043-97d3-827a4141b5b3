// services/eventsServices.ts

import {
  collection,
  addDoc,
  getDocs,
  getDoc,
  updateDoc,
  deleteDoc,
  doc,
  Timestamp,
  query,
  where,
  orderBy,
  setDoc,
} from "firebase/firestore";
import { initFirebase } from "../../firebaseConfig";
import { getAuth } from "firebase/auth";
import { FollowerManager } from "./followServices";
import { NotificationEvents, NotificationManager } from "./notificationService";
import { PublishingDateFilter } from "./filtersServices";
import { filterPostsByPublishingDate } from "./postService";
import { getAllUsers } from "./usersServices";

// Type definition for Event
export type Event = {
  id?: string;
  name: string;
  date: string;
  description: string;
  created_at?: Date | Timestamp;
  updated_at?: Date;
  deleted?: boolean;
  
  media?: string[] | any[];
  
};

// Collection name
const EVENTS_COLLECTION = "events";

/**
 * Create a new event in the Firestore collection.
 * @param eventData - Event data excluding id, created_at, and updated_at.
 * @returns Success status and the newly created document ID or an error message.
 */
export const createEvent = async (
  eventData: Omit<Event, "id" | "created_at" | "updated_at">
) => {
  try {
    const { db } = await initFirebase();

    // Create a reference to a new document with auto-generated ID
    const eventsRef = collection(db, EVENTS_COLLECTION);
    const newEventRef = doc(eventsRef);
    const _id = newEventRef.id;

    const auth = getAuth();
    const user = auth.currentUser;

    if (!user) {
      return { success: false, id: null };
    }    
    

    const newEvent = {
      ...eventData,
      id: _id,
      created_at: Timestamp.now(),
      updated_at: Timestamp.now(),
    };

    // Use setDoc instead of addDoc - this way we can specify the document ID
    await setDoc(doc(db, EVENTS_COLLECTION, _id), newEvent);

    // notification
    const followers:any[] = await FollowerManager.getInstance().GetFollowersByUserId(user?.uid);

    NotificationManager.getInstance().BulkCreateNotificationsAndUpdateUnreadCounts({
      event:NotificationEvents.EVENT_UPLOAD , 
      followers:followers?.map((c)=>{
        return {
          id:c?.id
        }
      }),
      userId:user?.uid , 
    })        
    

    return { success: true, id: _id };
  } catch (error) {
    // console.error("Error creating event:", error);
    return { success: false, error: "Failed to create event" };
  }
};

/**
 * Retrieve all events from the Firestore collection.
 * @returns Success status and an array of events or an error message.
 */
export const getAllEvents = async ( filters?:Omit<GetFiltersInput,"category"|"location">) => {
  try {
    const { db } = await initFirebase();

    const querySnapshot = await getDocs(collection(db, EVENTS_COLLECTION));

    let events: Event[] = querySnapshot.docs
      .map((doc) => ({
        id: doc.id,
        ...(doc.data() as Omit<Event, "id">),
      }))
      .filter((event: Event) => event.deleted !== true);
 const allEventIds: string[] = [];
    const userIdsMap: any = {};
      let allUsers :any[]= (await getAllUsers())?.users ?? [];
      allUsers.forEach((userDoc) => {
      const userData = userDoc;

      if (userData.events && Array.isArray(userData.events)) {
        allEventIds.push(...userData.events);
        userData.events.forEach((curr: any) => (userIdsMap[curr] = userDoc.id));
      }
    });
        let finalEvents = events.map((event) => ({
      ...event,
      user_id: userIdsMap[event?.id ?? ""],
    }));
    events = finalEvents;
      

   if(filters) {
      events = await FilterEventsWrapper({
        filters , events:finalEvents
      })
    }

    return { success: true, events };
  } catch (error) {
    // console.error("Error fetching events:", error);
    return { success: false, error: "Failed to fetch events" };
  }
};

/**
 * Retrieve a specific event by its ID.
 * @param id - The ID of the event to retrieve.
 * @returns Success status and the event data or an error message.
 */
export const getEventById = async (id: string) => {
  try {
    const { db } = await initFirebase();

    const docRef = doc(db, EVENTS_COLLECTION, id);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      return {
        success: true,
        event: { id: docSnap.id, ...docSnap.data() } as Event,
      };
    } else {
      return { success: false, error: "Event not found" };
    }
  } catch (error) {
    // console.error("Error fetching event by ID:", error);
    return { success: false, error: "Failed to fetch event" };
  }
};

// Helper function to fetch events in batches of 10 using "where in"
const fetchEventsInBatches = async (eventIds: string[], batchSize = 10) => {
  const eventDetails: any[] = [];
  const { db } = await initFirebase();

  for (let i = 0; i < eventIds.length; i += batchSize) {
    const batchIds = eventIds.slice(i, i + batchSize);

    // Firestore query to get events where the ID is in batchIds
    const eventsQuery = query(
      collection(db, "events"),
      where("__name__", "in", batchIds),
      orderBy("date", "desc")
    );
    const querySnapshot = await getDocs(eventsQuery);

    querySnapshot.forEach((doc) => {
      eventDetails.push({ id: doc.id, ...doc.data() });
    });
  }

  return eventDetails;
};



interface GetFiltersInput {
      category?:string[] , 
      user_id?:string[] , 
      location?:string[] , 
      date_of_publishing?:PublishingDateFilter
}

const FilterEventsWrapper = async ({
  filters , 
  events
}:{
    filters:GetFiltersInput , 
  events:Array<Event | any>
}) =>{ 
      /**
     *  filter stage
     */
    ///-------------------------------------------------------------------------------------
    let enrichedEvents = events ;
      let finalResp:Event[] = [];
    let filterApplied = false;

    if(filters?.date_of_publishing) { 
        enrichedEvents = filterPostsByPublishingDate(enrichedEvents,filters?.date_of_publishing,"events");
    }

      if(filters?.category?.length || filters?.location?.length || filters?.user_id?.length 
      ) { 
        filterApplied = true;
        for(let i = 0 ; i < enrichedEvents?.length; i++) { 
          let current = enrichedEvents?.[i];
          
        // category filter
        if(filters?.category?.length) {
          if(filters?.category?.includes("Literature")){
            filters.category = [...filters?.category , "Storytelling"];
          } 
          // @ts-ignore
          if(filters?.category?.includes(current?.category)) { 
              finalResp?.push(current);
          }
        }

        // location filter ❌ not applied in events
        if(filters?.location?.length) { 
          // @ts-ignore
          if(filters?.location.find((c)=>current?.location?.includes(c))) { 
              finalResp?.push(current);
          }
        }

        // user_id
        if(filters?.user_id?.length) { 
          if(current?.user_id && filters?.user_id?.includes(current?.user_id)) { 
             finalResp?.push(current);
          }
        }

        }
      }      
      
      if(!filterApplied) { 
        finalResp = enrichedEvents;
      }
    

    ///-------------------------------------------------------------------------------------
      return finalResp;
}
//
export const getEventsByCategory = async (
  category_name: string,
  currentUserId?: string,
  filters?:Omit<GetFiltersInput,"category"|"location">
) => {
  try {
    // Step 1: Find users where category[0] matches category_name
    // console.log({ currentUserId, category_name });
    const { db } = await initFirebase();

    if (category_name === "My Feed") {
      if (!currentUserId) {
        return { success: true, events: [] };
      }

      const currentUserRef = doc(db, "users", currentUserId); // Replace with actual user ID

      const currentUserSnap = await getDoc(currentUserRef);

      if (!currentUserSnap.exists()) {
        return { success: true, events: [] };
      }

      const currentUserData = currentUserSnap.data();
      const userIds = currentUserData.bookmarks || [];

      if (userIds.length === 0) {
        return { success: true, events: [] };
      }
      // console.log("in: ", { userIds });

      let events = await getEventsForUsers(userIds);
      let finalEvents = events?.events ?? [];
      if(filters) {
      finalEvents = await FilterEventsWrapper({
        filters , events:finalEvents
      })
    }
    
      return finalEvents;
    }

    const usersQuery = query(
      collection(db, "users"),
      where("categories", "array-contains", category_name)
    );
    const userSnapshots = await getDocs(usersQuery);
    // console.log({userSnapshots});

    if (userSnapshots.empty) {
      return { success: true, events: [] };
    }

    // Step 2: Collect event IDs from all users
    const allEventIds: string[] = [];
    const userIdsMap: any = {};

    userSnapshots.forEach((userDoc) => {
      const userData = userDoc.data();

      if (userData.events && Array.isArray(userData.events)) {
        allEventIds.push(...userData.events);
        userData.events.forEach((curr: any) => (userIdsMap[curr] = userDoc.id));
      }
    });

    if (allEventIds.length === 0) {
      return { success: true, events: [] };
    }

    // console.log({ userIdsMap });

    // Step 3: Fetch all events in batches of 10 using "where in"
    const eventDetails = await fetchEventsInBatches(allEventIds);

    // Step 4: Attach user_id to events (optional)
    let finalEvents = eventDetails.map((event) => ({
      ...event,
      user_id: userIdsMap[event.id],
      // userIds.find((id) => event.ownerId === id) || null, // Assuming events have an `ownerId`
    }));


    if(filters) {
      finalEvents = await FilterEventsWrapper({
        filters , events:finalEvents
      })
    }
    

    return { success: true, events: finalEvents };
  } catch (error) {
    console.error("Error fetching events by category:", error);
    return { success: false, error: "Failed to fetch events" };
  }
};

//
export const getEventsByUserId = async (userId: string) => {
  try {
    const { db } = await initFirebase();

    // Step 1: Fetch the user document
    const userRef = doc(db, "users", userId);
    const userSnap = await getDoc(userRef);

    if (!userSnap.exists()) {
      return { success: false, error: "User not found" };
    }

    // Step 2: Extract events array from user document
    const userData = userSnap.data();
    const eventIds = userData?.events ?? [];

    if (eventIds.length === 0) {
      return { success: true, events: [] };
    }

    // Step 3: Fetch events in batches using "where in"
    const eventDetails = await _fetchEventsInBatches(eventIds, userId);

    return { success: true, events: eventDetails };
  } catch (error) {
    console.error("Error fetching user events:", error);
    return { success: false, error: "Failed to fetch user events" };
  }
};

const _fetchEventsInBatches = async (
  eventIds: string[],
  userId: string,
  batchSize = 10
) => {
  const { db } = await initFirebase();

  const eventDetails: any[] = [];

  for (let i = 0; i < eventIds.length; i += batchSize) {
    const batchIds = eventIds.slice(i, i + batchSize);

    // Firestore query to get events where the ID is in batchIds
    const eventsQuery = query(
      collection(db, "events"),
      where("__name__", "in", batchIds),
      orderBy("date", "desc")
    );
    const querySnapshot = await getDocs(eventsQuery);

    querySnapshot.forEach((doc) => {
      eventDetails.push({ ...doc.data(), user_id: userId });
    });
  }

  return eventDetails;
};

export const getEventsForUsers = async (userIds: string[]) => {
  try {
    if (userIds.length === 0) {
      return { success: true, events: [] };
    }
    const { db } = await initFirebase();

    // Step 1: Fetch all event IDs from user documents
    const allEventIds: string[] = [];
    const eventUserMap: Record<string, string> = {}; // Map eventId -> userId

    const userPromises = userIds.map(async (userId) => {
      const userRef = doc(db, "users", userId);
      const userSnap = await getDoc(userRef);

      if (userSnap.exists()) {
        const userData = userSnap.data();
        const userEventIds = userData?.events ?? [];

        userEventIds.forEach((eventId: string) => {
          eventUserMap[eventId] = userId; // Store event-user relationship
        });

        allEventIds.push(...userEventIds);
      }
    });

    await Promise.all(userPromises); // Wait for all user event retrieval

    if (allEventIds.length === 0) {
      return { success: true, events: [] };
    }

    // Step 2: Fetch event details in batches
    const eventDetails = await _fetchEventsInBatchesForUsers(
      allEventIds,
      eventUserMap
    );

    return { success: true, events: eventDetails };
  } catch (error) {
    console.error("Error fetching events for users:", error);
    return { success: false, error: "Failed to fetch events" };
  }
};

const _fetchEventsInBatchesForUsers = async (
  eventIds: string[],
  eventUserMap: Record<string, string>,
  batchSize = 10
) => {
  const eventDetails: any[] = [];
  const { db } = await initFirebase();

  for (let i = 0; i < eventIds.length; i += batchSize) {
    const batchIds = eventIds.slice(i, i + batchSize);

    // Firestore query to get events where ID is in batchIds
    const eventsQuery = query(
      collection(db, "events"),
      where("__name__", "in", batchIds),
      orderBy("date", "desc")
    );

    const querySnapshot = await getDocs(eventsQuery);

    querySnapshot.forEach((doc) => {
      eventDetails.push({
        id: doc.id,
        ...doc.data(),
        user_id: eventUserMap[doc.id],
      });
    });
  }

  return eventDetails;
};

/**
 * Update an existing event in the Firestore collection.
 * @param id - The ID of the event to update.
 * @param updatedData - Partial data to update the event.
 * @returns Success status or an error message.
 */
export const updateEvent = async (
  id: string,
  updatedData: Partial<Omit<Event, "id" | "created_at">>
) => {
  try {
    const auth = getAuth();
    const user = auth.currentUser;        
    if(!user?.uid){
      return { success: false };
    }    
    const {db} =  await initFirebase();
    
    //////////////////////////////////////////////////////////// Authorization check
     const usersCollection = collection(db, "users");
     const q = query(usersCollection, where("events", "array-contains", id));
     const userSnap = await getDocs(q);
     if (!userSnap.empty) {
       const userDoc = userSnap.docs[0]; 
       if(userDoc?.data()?.id !== user?.uid){
         return { success: false };
       }
     }    
     ////////////////////////////////////////////////////////////
     
    const docRef = doc(db, EVENTS_COLLECTION, id);
    await updateDoc(docRef, {
      ...updatedData,
      updated_at: Timestamp.now(),
    });

    return { success: true };
  } catch (error) {
    // console.error("Error updating event:", error);
    return { success: false, error: "Failed to update event" };
  }
};

/**
 * Delete an event from the Firestore collection by its ID.
 * @param id - The ID of the event to delete.
 * @returns Success status or an error message.
 */
export const deleteEvent = async (id: string) => {
  try {
    const { db } = await initFirebase();

    const auth = getAuth();
    const user = auth.currentUser;        
    if(!user?.uid){
      return { success: false };
    }        
    //////////////////////////////////////////////////////////// Authorization check
    const usersCollection = collection(db, "users");
    const q = query(usersCollection, where("events", "array-contains", id));
    const userSnap = await getDocs(q);
    if (!userSnap.empty) {
      const userDoc = userSnap.docs[0]; 
      if(userDoc?.data()?.id !== user?.uid){
        return { success: false };
      }
    }    
    ////////////////////////////////////////////////////////////
        
    

    const docRef = doc(db, EVENTS_COLLECTION, id);
    await deleteDoc(docRef);

    return { success: true };
  } catch (error) {
    // console.error("Error deleting event:", error);
    return { success: false, error: "Failed to delete event" };
  }
};
