import React, { useState } from "react";
import NestedDrawer from "../ui/nested-drawer";
import * as Tabs from "@radix-ui/react-tabs";
import { TabsContent } from "@/components/ui/tabs";
import { MoreHorizontal } from "react-feather";
import ChangeStatusDrawer from "./ChangeStatusDrawer";
import OrderDetailsTab from "./OrderDetailsTab";
import OrderActivityTab from "./OrderActivityTab";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MailServiceManager } from "@/services/MailService";
import { ChatManager } from "@/services/chatService";
import { Badge } from "@/components/ui/badge";
import { Modal, ModalBody, ModalContent, ModalHeader } from "@heroui/react";
import { Button } from "../ui/button";

interface OrderDetailProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  selectedOrderItem: string | number | null;
  order: any;
  currencySymbol?: string;
  currencyCode?: string;
  userId: string;
  refreshOrders: () => void; // <-- add this line
  onDrawerChange: (open: boolean) => void;
}

const OrderDetail: React.FC<OrderDetailProps> = ({
  isOpen,
  onOpenChange,
  selectedOrderItem,
  order,
  currencySymbol = "$",
  currencyCode = "USD",
  userId,
  refreshOrders,
  onDrawerChange,
}) => {
  // console.log({ order });

  // Get current user information from localStorage
  const getCurrentUserInfo = () => {
    try {
      const userStr = localStorage.getItem("user");
      if (userStr) {
        const userData = JSON.parse(userStr);
        return userData;
      }
    } catch (error) {
      console.error("Error parsing user from localStorage:", error);
    }
    return null;
  };

  const currentUser = getCurrentUserInfo();
  // console.log({ currentUser });

  if (!order) return null;

  const [isChangeStatusOpen, setIsChangeStatusOpen] = useState(false);
  const [isRequestCancelOpen, setIsRequestCancelOpen] = useState(false);
  const [isOpenMailModal, setIsOpenMailModal] = useState(false);

  const handleStatusChange = (newStatus: string) => {
    // Here you would typically update the order status in your backend
    // console.log("Changing status to:", newStatus);
  };

  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  return (
    <>
      <NestedDrawer
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        title={`Order #${order?.orderNumber || order.uniqueId || order.id || ""}`}
      >
        <div className="flex flex-col -mt-4">
          <div className="px-4">
            <div className="flex items-center justify-between mb-6 mt-4">
              <div>
                <p className="text-sm text-gray-500">Current Status:</p>
                <p className="text-base font-semibold text-primary">{order.status}</p>
              </div>
              <div className="flex items-center gap-2">
                {(() => {
                  const orderType = order.userProfileId === userId ? "placed" : "received";
                  const isCompleted =
                    order.status === "COMPLETED" ||
                    order.status === "CANCELLED" ||
                    order.status === "AUTO-COMPLETED" ||
                    order.status === "AUTO-DECLINED" ||
                    order.status === "DECLINED";

                  if (isCompleted) {
                    return false;
                  }
                  if (orderType == "received" && order.status === "DELIVERED") {
                    return false;
                  }
                  if (orderType == "received") {
                    return true;
                  }
                  if (orderType == "placed") {
                    const allowedStatuses = ["DELIVERED"];
                    return allowedStatuses.includes(order.status);
                  }

                  return false;
                })() && (
                  <button
                    onClick={() => setIsChangeStatusOpen(true)}
                    className="px-4 py-1 text-sm bg-primary text-white rounded-full hover:bg-gray-800 transition-colors"
                  >
                    Change Status
                  </button>
                )}

                <div>
                  <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
                    <DropdownMenuTrigger asChild className="justify-end">
                      <MoreHorizontal className=" cursor-pointer" />
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-60 rounded-3xl z-50">
                      {order.userProfileId === userId ? (
                        <DropdownMenuLabel
                          key={order.userProfileId}
                          className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                          onClick={async () => {
                            if (order?.profileDetails?.email) {
                              await MailServiceManager.getInstance().sendMail({
                                toMail: order?.profileDetails?.email ?? "",
                                type: "order_contact",
                                message: {
                                  orderId: order?.uniqueId,
                                  name: order?.userProfileDetails?.profile_name,
                                },
                              });
                              await ChatManager.getInstance().SendMessage({
                                message: "hello I have a query regarding order " + order?.uniqueId,
                                recipientId: order?.profileDetails?.id,
                                recipientName: order?.profileDetails?.profile_name,
                                senderId: order?.userProfileDetails?.id,
                                senderName: order?.userProfileDetails?.profile_name,
                              });

                              setIsOpenMailModal(true);
                              setIsDropdownOpen(false);
                            }
                          }}
                        >
                          Contact Seller
                        </DropdownMenuLabel>
                      ) : (
                        <DropdownMenuLabel
                          className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                          key={order.userProfileId}
                          onClick={async () => {
                            if (order?.userProfileDetails?.email) {
                              await MailServiceManager.getInstance().sendMail({
                                toMail: order?.userProfileDetails?.email ?? "",
                                type: "order_contact",
                                message: {
                                  orderId: order?.uniqueId,
                                  name: order?.profileDetails?.profile_name,
                                },
                              });
                              await ChatManager.getInstance().SendMessage({
                                message: "hello I have a query regarding order " + order?.uniqueId,
                                senderId: order?.profileDetails?.id,
                                senderName: order?.profileDetails?.profile_name,
                                recipientId: order?.userProfileDetails?.id,
                                recipientName: order?.userProfileDetails?.profile_name,
                              });

                              setIsOpenMailModal(true);
                            }
                            setIsDropdownOpen(false);
                          }}
                        >
                          Contact Customer
                        </DropdownMenuLabel>
                      )}

                      {order.status !== "COMPLETED" && (
                        <DropdownMenuLabel
                          className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                          onClick={() => {
                            setIsRequestCancelOpen(true);
                            setIsDropdownOpen(false);
                          }}
                        >
                          Request Cancellation
                        </DropdownMenuLabel>
                      )}
                      <DropdownMenuLabel
                        className="text-center font-normal text-base cursor-pointer"
                        onClick={() => {
                          const orderId = order?.orderNumber || order.uniqueId || order.id || "";
                          window.location.href =
                            `mailto:<EMAIL>?` +
                            `subject=${encodeURIComponent("AMUZN Customer Support " + (currentUser?.profile_name ?? "") + " Order #" + orderId)}` +
                            `&body=${encodeURIComponent("Hello,\n\nPlease enter you comment here.\n\nThank you")}`;
                        }}
                      >
                        Contact Customer Support
                      </DropdownMenuLabel>

                      <DropdownMenuSeparator />
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </div>

            <Tabs.Root defaultValue="details" className="w-full">
              <div className="TabsListBg w-full flex rounded-md bg-none p-1">
                <Tabs.List className="w-full flex h-[30px]" aria-label="Order details">
                  <Tabs.Trigger
                    className="TabsTriggerBg flex-1 py-2 rounded-md data-[state=active]:bg-[#BDBDBD] data-[state=active]:text-primary"
                    value="details"
                  >
                    Details
                  </Tabs.Trigger>
                  <Tabs.Trigger
                    className="TabsTriggerBg flex-1 py-2 rounded-md data-[state=active]:bg-[#BDBDBD] data-[state=active]:text-primary"
                    value="activity"
                  >
                    Activity log
                  </Tabs.Trigger>
                </Tabs.List>
              </div>

              <div className="mt-6">
                <TabsContent value="details">
                  <OrderDetailsTab
                    order={order}
                    currencySymbol={currencySymbol}
                    currencyCode={currencyCode}
                  />
                </TabsContent>

                <TabsContent value="activity">
                  <OrderActivityTab
                    order={order}
                    orderType={order.userProfileId === userId ? "placed" : "received"}
                  />
                </TabsContent>
              </div>
            </Tabs.Root>
          </div>
        </div>
      </NestedDrawer>

      <ChangeStatusDrawer
        isOpen={isChangeStatusOpen}
        onOpenChange={setIsChangeStatusOpen}
        currentStatus={order.status}
        onStatusChange={(status) => {
          handleStatusChange(status);
          refreshOrders();
        }}
        orderType={order.userProfileId === userId ? "placed" : "received"}
        orderId={order.id}
        loggedInUser={currentUser?.profile_name || currentUser?.displayName || "Unknown User"}
        sellerName={order.profileDetails?.profile_name || "Unknown Seller"}
        userName={order.userProfileDetails?.profile_name || "Unknown User"}
      />

      {/* Signout Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpenMailModal}
          placement="auto"
          onOpenChange={setIsOpenMailModal}
          hideCloseButton={true}
        >
          <ModalContent className="modal-content w-80 p-12 rounded-3xl">
            {() => (
              <>
                <ModalBody>
                  <p className="text-center text-black text-lg">Email sent successfully</p>
                  <div>
                    <Button
                      variant="outline"
                      className="rounded-full w-full mt-5 border-black text-black border-2 py-5 text-base"
                      onClick={() => {
                        setIsOpenMailModal(false);
                        onOpenChange(false); // Close the order drawer
                        onDrawerChange(false);
                        // Navigate to chats sidebar
                        const currentUrl = new URL(window.location.href);
                        currentUrl.searchParams.set("sidebar", "chat");
                        window.history.pushState({}, "", currentUrl.toString());
                        // Trigger sidebar to open
                        window.dispatchEvent(new CustomEvent("openSidebar"));
                      }}
                    >
                      Open Chat
                    </Button>
                    <Button
                      variant="outline"
                      className="rounded-full w-full mt-3 border-black text-black border-2 py-5 text-base"
                      onClick={() => setIsOpenMailModal(false)}
                    >
                      cancel
                    </Button>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>
    </>
  );
};

export default OrderDetail;
