import { User } from "@/services/UserInterface";
import { GetFiltersInput, userFilterWrapper } from "@/services/usersServices";
import { getFirestore } from "firebase-admin/firestore";
import { FollowerHandlerManager } from "./follow-handlers";
import { Send<PERSON>ailHandler } from "./handlers";

export class UsersHandlerManager {
  private USERS_COLLECTION = "users";

  static instance: UsersHandlerManager | null = null;

  private constructor() {}

  static getInstance() {
    if (!this.instance) {
      this.instance = new UsersHandlerManager();
    }
    return this.instance;
  }
  private _sanitizeUserPayload(data: User) {
    const { stripe_id, email, basketOrdersCount, password, ...safeData } = data;

    return safeData;
  }

  GetAllUsers = async ({
    filters,
    uid,
  }: {
    filters?: Omit<GetFiltersInput, "date_of_publishing">;
    uid?: string;
  }) => {
    try {
      const db = getFirestore();

      const snapshot = await db.collection("users").get();

      let usersList: any[] = snapshot.docs
        .map(
          (doc) =>
            ({
              id: doc.id,
              ...doc.data(),
            }) as User
        )
        .filter((user) => user.isDeleted !== true);

      usersList = usersList?.map((c) => {
        return this._sanitizeUserPayload(c);
      });

      if (filters) {
        usersList = await userFilterWrapper({ filters, users: usersList });
      }
      console.log({ uid });

      // ✅ Add is_followed_by_me field
      if (uid) {
        const myFollowers = await FollowerHandlerManager.getInstance()?.GetFollowingsByUserId(uid);

        const followedIds = myFollowers.map((f: any) => (typeof f === "string" ? f : f.id));
        console.log({ followedIds });

        usersList = usersList.map((u) => ({
          ...u,
          is_followed_by_me: followedIds.includes(u.id),
        }));
      } else {
        // if no uid passed, default false
        usersList = usersList.map((u) => ({
          ...u,
          is_followed_by_me: false,
        }));
      }

      return usersList;
    } catch (error) {
      console.error("Error fetching users:", error);
      return { success: false, error: "Server error" };
    }
  };

  DeleteUserDetails = async ({ user_id, comment }: { user_id: string; comment: string }) => {
    try {
      if (!user_id) {
        throw new Error("user_id is required");
      }

      // 🚀
      // validate if user has active service then cannt delete

      // Perform cascading updates (mark user + related docs deleted)
      const results = await Promise.allSettled([
        this.updateUserDeleteFlag({ user_id, flag: true, comment }),
        this.updatePostDeleteFlag({ user_id, flag: true }),
        this.updateEventsDeleteFlag({ user_id, flag: true }),
        this.updateServicesDeleteFlag({ user_id, flag: true }),
        // updateOrdersDeleteFlag({ user_id, flag: true }),
        // updateAuthBridgeDeleteFlag({ user_id }),
        // updateFollowDeleteFlag({ user_id }),
        // updateCommentsDeleteFlag({ user_id, flag: true }),
      ]);

      // Delete the Firebase Auth user using Admin SDK
      // await admin.auth().deleteUser(user_id);
      const db = getFirestore();

      const userRef = db.collection("users").doc(user_id);
      await userRef.delete();

      // Send notification email
      await SendMailHandler({
        payload: {
          to: "<EMAIL>",
        },
        type: "profile_deleted",
        mail_type: "profile_deleted",
        message: {
          profileId: user_id,
          reason: comment,
        },
      });

      return {
        success: true,
        message: "User deletion process completed",
        results,
      };
    } catch (error) {
      console.error("❌ deleteUserDetails failed:", error);
      throw new Error("delete user details failed");
    }
  };

  async updateUserDeleteFlag({
    user_id,
    flag,
    comment,
  }: {
    user_id: string;
    flag: boolean;
    comment: string;
  }) {
    try {
      const db = getFirestore();
      const oldRef = db.collection("users").doc(user_id);
      const snapshot = await oldRef.get();

      if (snapshot.exists) {
        const data = snapshot.data();

        const newRef = db.collection("deleted_users").doc(user_id);
        await newRef.set(
          {
            ...data,
            delete_reason: comment,
            user_id_rem: user_id,
            isDeleted: true,
          },
          { merge: true }
        );

        await oldRef.delete();
      }

      return { success: true };
    } catch (error) {
      console.error("❌ user_delete_flag_failed:", error);
      throw new Error("user_delete_flag_failed");
    }
  }

  /**
   * Move all posts of a user to deleted_posts and delete originals.
   */
  async updatePostDeleteFlag({ user_id, flag }: { user_id: string; flag: boolean }) {
    try {
      const db = getFirestore();
      const userRef = db.collection("users").doc(user_id);
      const userSnap = await userRef.get();

      if (!userSnap.exists) throw new Error("User not found");

      const userData = userSnap.data() || {};
      const postIds: string[] = userData.posts || [];

      if (postIds.length === 0) {
        return { success: true, message: "No posts to update" };
      }

      for (const postId of postIds) {
        const postRef = db.collection("posts").doc(postId);
        const postSnap = await postRef.get();

        if (!postSnap.exists) continue;

        const postData = postSnap.data();

        await db
          .collection("deleted_posts")
          .doc(postId)
          .set({
            ...postData,
            deleted: true,
            user_id_rem: user_id,
            post_id_rem: postId,
          });

        await postRef.delete();
      }

      return { success: true, updatedPosts: postIds };
    } catch (error) {
      console.error("❌ post_delete_flag_failed:", error);
      throw new Error("post_delete_flag_failed");
    }
  }

  /**
   * Move all events of a user to deleted_events and delete originals.
   */
  async updateEventsDeleteFlag({ user_id, flag }: { user_id: string; flag: boolean }) {
    try {
      const db = getFirestore();
      const userRef = db.collection("users").doc(user_id);
      const userSnap = await userRef.get();

      if (!userSnap.exists) throw new Error("User not found");

      const userData = userSnap.data() || {};
      const eventIds: string[] = userData.events || [];

      if (eventIds.length === 0) {
        return { success: true, message: "No events to update" };
      }

      for (const eventId of eventIds) {
        const eventRef = db.collection("events").doc(eventId);
        const eventSnap = await eventRef.get();

        if (!eventSnap.exists) continue;

        const eventData = eventSnap.data();

        await db
          .collection("deleted_events")
          .doc(eventId)
          .set({
            ...eventData,
            deleted: true,
            user_id_rem: user_id,
            event_id_rem: eventId,
          });

        await eventRef.delete();
      }

      return { success: true, updatedEvents: eventIds };
    } catch (error) {
      console.error("❌ event_delete_flag_failed:", error);
      throw new Error("event_delete_flag_failed");
    }
  }

  /**
   * Move all services of a user to deleted_services and delete originals.
   */
  async updateServicesDeleteFlag({ user_id, flag }: { user_id: string; flag: boolean }) {
    try {
      const db = getFirestore();
      const userRef = db.collection("users").doc(user_id);
      const userSnap = await userRef.get();

      if (!userSnap.exists) throw new Error("User not found");

      const userData = userSnap.data() || {};
      const serviceIds: string[] = userData.services || [];

      // reset services list
      await userRef.update({ services: [] });

      if (serviceIds.length === 0) {
        return { success: true, message: "No services to update" };
      }

      for (const serviceId of serviceIds) {
        const serviceRef = db.collection("services").doc(serviceId);
        const serviceSnap = await serviceRef.get();

        if (!serviceSnap.exists) continue;

        const serviceData = serviceSnap.data();

        await db
          .collection("deleted_services")
          .doc(serviceId)
          .set({
            ...serviceData,
            deleted: true,
            user_id_rem: user_id,
            service_id_rem: serviceId,
          });

        await serviceRef.delete();
      }

      return { success: true, updatedServices: serviceIds };
    } catch (error) {
      console.error("❌ service_delete_flag_failed:", error);
      throw new Error("service_delete_flag_failed");
    }
  }

  async updateFollowDeleteFlag(user_id: string) {
    try {
      const db = getFirestore();
      const userRef = db.collection("users").doc(user_id);
      const userSnap = await userRef.get();

      if (!userSnap.exists) throw new Error("User not found");

      const userData = userSnap.data() || {};
      const followers: string[] = userData.followers || [];
      const following: string[] = userData.bookmarks || []; // assuming bookmarks = following

      const batch = db.batch();

      // Remove user_id from each follower's "following" array
      followers.forEach((followerId) => {
        const followerRef = db.collection("users").doc(followerId);
        batch.update(followerRef, {
          following: admin.firestore.FieldValue.arrayRemove(user_id),
        });
      });

      // Remove user_id from each following user's "followers" array
      following.forEach((followingId) => {
        const followingRef = db.collection("users").doc(followingId);
        batch.update(followingRef, {
          followers: admin.firestore.FieldValue.arrayRemove(user_id),
        });
      });

      await batch.commit();

      return {
        success: true,
        message: "User removed from all follow relationships",
      };
    } catch (error) {
      console.error("❌ follow_delete_failed:", error);
      throw new Error("follow_delete_failed");
    }
  }

  /**
   * Hide all comments made by a user (soft delete).
   */
  async updateCommentsDeleteFlag(user_id: string, flag: boolean) {
    try {
      const db = getFirestore();
      const commentsRef = db.collection("comments");
      const q = commentsRef.where("user_id", "==", user_id);
      const snapshot = await q.get();

      if (snapshot.empty) {
        return { success: true, message: "No comments found for user" };
      }

      const batch = db.batch();
      snapshot.forEach((docSnap) => {
        batch.update(docSnap.ref, { hidden: flag });
      });

      await batch.commit();

      return {
        message: "User deletion process completed",
      };
    } catch (error) {
      console.error("❌ comments_update_del_flag_failed:", error);
      throw new Error("comments_update_del_flag_failed");
    }
  }
}
