import { NextRequest, NextResponse } from "next/server";
import { getAuth } from "firebase-admin/auth";
import { DecodedIdToken } from "firebase-admin/auth"; 
import { initAdmin } from "../../firebaseAdminConfig";

export async function verifyAuth(request: NextRequest): Promise<{
  uid: string;
  decodedToken: DecodedIdToken;
} | NextResponse> {
  try {
    await initAdmin();

    const authHeader = request.headers.get("authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const idToken = authHeader.split("Bearer ")[1];

    const decodedToken = await getAuth().verifyIdToken(idToken);
    return { uid: decodedToken.uid, decodedToken };
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: "Invalid or expired token",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 403 }
    );
  }
}
