import { useCallback, useEffect, useRef, useState } from "react";
import { getAllUsers, getUserById } from "@/services/usersServices";
import { themes } from "../../../../theme";
import GlobalProfileCard from "@/globalComponents/globalProfileCard";
import useAuth from "@/hook";
import useProfile from "@/hook/profileData";
import { getLensProfilesById } from "@/services/lensService";
import { useProfilesQuery } from "@/graphql/generated";
import { useFilter } from "@/context/FilterContext";
import GlobalProfileCardLens from "@/globalComponents/globalProfileCardLens";
import { sortProfiles } from "@/lib/helper";
import { FollowerManager } from "@/services/followServices";
import ProfileCardSkeleton from "@/components/CardSkeleton/ProfileCardSkeleton";
import {
  AccountsBulkQuery,
  FollowingOrderBy,
  PageSize,
  useAccountsBulkQuery,
  useFollowingQuery,
} from "@/graphql/test/generated";
import { getLocation } from "@/lib/utils";
import { getId } from "@/services/authBridgeService";
import { useAccount } from "wagmi";

const ProfileCard = (props: any) => {
  const user = useAuth();
  const { filters, getServiceFilters } = useFilter();
  const { address, isConnected } = useAccount();

  const isAuthLogin = user.isLogin;
  // console.log(isAuthLogin);

  const { profileData } = useProfile(user?.userId || "");
  const [categoryData, setCategoryData] = useState<Record<string, any[]>>({});
  const [profilesData, setProfilesData] = useState<AccountsBulkQuery["accountsBulk"]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  const categories = [
    "Music",
    "Literature",
    "Art",
    "Film & Photography",
    "Theatre & Performance",
    "Multidisciplinary",
    "Groups",
    "Storytelling",
  ];

  console.log({ filters });

  // Fetch users from API
  const fetchAllUsers = useCallback(async () => {
    try {
      setLoading(true);
      // Get complete filter object for service call
      const serviceFilters = getServiceFilters();
      console.log({ serviceFilters });

      const response = await getAllUsers(serviceFilters);
      if (Array.isArray(response?.users) && response.users.length > 0) {
        const categorizedData: Record<string, any[]> = {};
        let myFeed: any[] = [];

        for (const category of categories) {
          const filteredPosts = response.users.filter((post: any) => {
            const userCategory = post?.categories?.[0];
            // Skip users without a profile name
            if (!post.profile_name) return false;

            return category === "Literature"
              ? userCategory === category || userCategory === "Storytelling"
              : userCategory === category;
          });

          categorizedData[category] = filteredPosts;
          // console.log({ user })
        }

        // console.log(isAuthLogin);
        // console.log(user.userId);
        if (user.isLogin) {
          const resp1 = await FollowerManager.getInstance().GetFollowingsByUserId(user.userId);

          myFeed = resp1;
        }

        if (user.isLogin) {
          categorizedData["My Feed"] = myFeed;
        }
        // console.log({ categorizedData });

        setCategoryData(categorizedData);
      }
    } catch (error) {
      console.error("Error fetching posts:", error);
    } finally {
      setLoading(false);
    }
  }, [user.userId, filters]);

  useEffect(() => {
    fetchAllUsers();
  }, [user.userId, filters]);

  // Fetch Lens profiles by category
  const [profiles, setProfiles] = useState<
    Array<{
      localName: string;
    }>
  >([]);
  const [profilesmy, setProfilesMy] = useState<
    Array<{
      localName: string;
    }>
  >([]);
  // const [profilesData, setProfilesData] = useState<any>(null);
  const category = props.themeProperties.title.toLowerCase();
  const profilesRef = useRef<
    Array<{
      localName: string;
    }>
  >([]);

  useEffect(() => {
    const fetchLensProfilesByCategory = async () => {
      const resp = await getLensProfilesById(category);
      const lensProfiles: Array<{
        localName: string;
      }> = resp?.lens_ids?.map((curr: any) => {
        return {
          localName: curr,
        };
      });

      if (JSON.stringify(profilesRef.current) !== JSON.stringify(lensProfiles)) {
        profilesRef.current = lensProfiles;
        setProfiles(lensProfiles);
      }
    };

    fetchLensProfilesByCategory();
  }, [category]);

  const {
    data: profileDataByLensId,
    error: profileError,
    isLoading: loadingProfile,
  } = useAccountsBulkQuery(
    {
      request: {
        usernames:
          props.themeProperties.title === "My Feed"
            ? profilesmy
            : filters.profileName
              ? [{ localName: filters.profileName }]
              : profiles,
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled:
        props.themeProperties.title == "My Feed"
          ? profilesmy.length > 0
          : filters.profileName
            ? [{ localName: filters.profileName }].length > 0
            : profiles.length > 0,
    }
  );

  useEffect(() => {
    if (profileDataByLensId) {
      setProfilesData(profileDataByLensId?.accountsBulk);
    }
  }, [profileDataByLensId]);

  useEffect(() => {
    if (profilesData) {
      // console.log({ profilesData });
    }
  }, [profilesData]);

  const mergedProfiles = sortProfiles(
    [
      ...(categoryData[props.themeProperties.title] || []).map((item) => ({
        ...item,
        profile_name: item.profile_name || "Profile Name*",
      })),
      ...profilesData.map((profile) => ({
        id: profile.username?.localName,
        profile_name: profile.metadata?.name || "Profile Name*",
        avatar: profile?.metadata?.picture || "",
        location: getLocation(profile.metadata?.attributes) || profile.username?.localName,
        isFollow: profile?.operations?.isFollowedByMe,
        lensProfile: true, // Mark lens profiles
      })),
    ],
    "profile_name"
  );

  // my feed

  const [currentFollowingCursor, setCurrentFollowingCursor] = useState<string | null>(null);
  const [userId, setUserId] = useState("");
  const containerRef = useRef<HTMLDivElement>(null);

  const getLensUserId = async (otherUserID: any) => {
    try {
      const resp = await getId({ id: user.userId });
      if (resp) {
        setUserId(resp?.lens_code);
      }
    } catch (error) {
      console.log({ error });
    }
  };

  useEffect(() => {
    getLensUserId(user.userId);
  }, [user.userId]);
  const { data: following, isLoading: following_loading } = useFollowingQuery(
    {
      request: {
        account: userId, // address
        pageSize: PageSize.Fifty,
        orderBy: FollowingOrderBy.Desc,
        cursor: currentFollowingCursor,
      },
    },
    {
      refetchOnWindowFocus: false,
    }
  );

  interface FollowingItem {
    localName: any;
  }

  const [allFollowing, setAllFollowing] = useState<FollowingItem[]>([]);

  useEffect(() => {
    if (
      following?.following?.items &&
      following?.following?.items.length > 0 &&
      props.themeProperties.title == "My Feed"
    ) {
      const newArray: FollowingItem[] = following.following.items.map((item: any) => ({
        localName: item.following.username?.localName,
      }));

      if (props.themeProperties.title == "My Feed") {
        setAllFollowing((prev) => [...prev, ...newArray]);
      }

      setProfiles((prev) => [...prev, ...newArray]);
    }
  }, [following, filters]);

  useEffect(() => {
    if (allFollowing) {
      setProfilesMy(allFollowing);
    }
  }, [allFollowing]);
  return (
    <>
      {loading ? (
        <div className="">
          <ProfileCardSkeleton count={6} columns={1} showGrid={true} />
        </div>
      ) : (
        <div className="w-full mt-0">
          {Object.entries(themes).map(([themeName, themeProperties], indexM) => (
            <div key={indexM}>
              {props.themeProperties.title === themeProperties.title && (
                <div>
                  {(() => {
                    const currentCategory = themeProperties.title;

                    // Check if filters are applied and if current category is in selected categories
                    if (filters.categories && filters.categories.length > 0) {
                      if (!filters.categories.includes(currentCategory)) {
                        // Current category is not in selected filters, show no data
                        return (
                          <div className="w-full mt-2 p-4 bg-gray-50 rounded-md border border-gray-200 text-center">
                            <p className="text-gray-500">No profiles found in this category.</p>
                          </div>
                        );
                      }
                    }

                    return mergedProfiles.length > 0 ? (
                      mergedProfiles.map((post, index) => (
                        <div key={index} className="mt-0">
                          {themeProperties.title === "My Feed" && !post.lensProfile ? (
                            Object.entries(themes).map(([_, innerThemeProperties], idx) => (
                              <div key={idx}>
                                {innerThemeProperties.title ===
                                  (post?.categories[0] === "Storytelling"
                                    ? "Literature"
                                    : post?.categories[0]) && (
                                  <GlobalProfileCard
                                    themeProperties={innerThemeProperties}
                                    isFollow={!profileData?.followers.includes(post.id)}
                                    location={post.location}
                                    profile_name={post.profile_name}
                                    avatar={post.avatar}
                                    id={post.id}
                                  />
                                  // <p>{post.profile_name}hii</p>
                                )}
                              </div>
                            ))
                          ) : themeProperties.title === "My Feed" && post.lensProfile ? (
                            <div>
                              <GlobalProfileCardLens
                                themeProperties={themeProperties}
                                isFollow={post.isFollow}
                                location={post.location}
                                profile_name={post.profile_name}
                                avatar={post.avatar}
                                id={post.id}
                              />
                            </div>
                          ) : post.lensProfile ? (
                            <GlobalProfileCardLens
                              themeProperties={themeProperties}
                              isFollow={post.isFollow}
                              location={post.location}
                              profile_name={post.profile_name}
                              avatar={post.avatar}
                              id={post.id}
                            />
                          ) : (
                            <GlobalProfileCard
                              themeProperties={themeProperties}
                              isFollow={!profileData?.followers.includes(post.id)}
                              location={post.location}
                              profile_name={post.profile_name}
                              avatar={post.avatar}
                              id={post.id}
                            />
                          )}
                        </div>
                      ))
                    ) : (
                      <p className="text-gray-500">No Profile available in this category.</p>
                    );
                  })()}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </>
  );
};

export default ProfileCard;
